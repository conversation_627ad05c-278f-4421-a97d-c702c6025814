<?php
/**
 * Script untuk memperbaiki meta field _novel_id pada chapters
 * Akses: yoursite.com/wp-content/themes/ln-reader/fix-chapter-meta.php
 * 
 * PERINGATAN: Backup database sebelum menjalankan script ini!
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin only.');
}

// Safety check
$confirm = isset($_GET['confirm']) && $_GET['confirm'] === 'yes';
$dry_run = !isset($_GET['execute']) || $_GET['execute'] !== 'true';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Chapter Meta Fields</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 4px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>

<h1>Fix Chapter Meta Fields</h1>

<?php if (!$confirm): ?>
<div class="section error">
    <h3>⚠️ PERINGATAN PENTING</h3>
    <p><strong>Script ini akan memodifikasi database WordPress Anda!</strong></p>
    <p>Pastikan Anda sudah:</p>
    <ul>
        <li>✅ Backup database</li>
        <li>✅ Test di environment development terlebih dahulu</li>
        <li>✅ Memahami apa yang akan dilakukan script ini</li>
    </ul>
    <p><a href="?confirm=yes" class="btn btn-warning">Saya Sudah Backup Database, Lanjutkan</a></p>
</div>
<?php else: ?>

<div class="section info">
    <h3>Mode: <?php echo $dry_run ? 'DRY RUN (Preview Only)' : 'EXECUTE (Akan Mengubah Database)'; ?></h3>
    <?php if ($dry_run): ?>
        <p>Ini adalah preview. Tidak ada perubahan yang akan disimpan ke database.</p>
        <p><a href="?confirm=yes&execute=true" class="btn btn-danger">EXECUTE - Jalankan Perubahan Sebenarnya</a></p>
    <?php else: ?>
        <p><strong>MODE EXECUTE AKTIF - Perubahan akan disimpan ke database!</strong></p>
    <?php endif; ?>
</div>

<?php
// Get all novels (assuming they are custom post type 'novel' or posts with specific meta)
$novels = get_posts([
    'post_type' => 'novel',
    'posts_per_page' => -1,
    'post_status' => 'publish'
]);

// If no novels found, try to find posts that might be novels
if (empty($novels)) {
    // Look for posts that have novel-related meta fields
    $novels = get_posts([
        'post_type' => 'post',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => [
            'relation' => 'OR',
            [
                'key' => '_novel_author',
                'compare' => 'EXISTS'
            ],
            [
                'key' => '_novel_status',
                'compare' => 'EXISTS'
            ],
            [
                'key' => '_alternative_title',
                'compare' => 'EXISTS'
            ]
        ]
    ]);
}

echo '<div class="section">';
echo '<h3>Novels Ditemukan: ' . count($novels) . '</h3>';

if (!empty($novels)) {
    echo '<table>';
    echo '<tr><th>ID</th><th>Title</th><th>Type</th><th>Chapters Found</th><th>Action</th></tr>';
    
    $total_fixed = 0;
    
    foreach ($novels as $novel) {
        $novel_id = $novel->ID;
        $novel_title = $novel->post_title;
        $novel_slug = $novel->post_name;
        
        // Try to find chapters for this novel
        $chapters_found = [];
        
        // Method 1: Already have _novel_id
        $existing_chapters = get_posts([
            'post_type' => 'post',
            'meta_key' => '_novel_id',
            'meta_value' => $novel_id,
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ]);
        
        // Method 2: Search by novel slug
        $slug_chapters = get_posts([
            'post_type' => 'post',
            'posts_per_page' => -1,
            's' => $novel_slug,
            'post_status' => 'publish',
            'post__not_in' => [$novel_id]
        ]);
        
        // Method 3: Search by novel title
        $title_chapters = get_posts([
            'post_type' => 'post',
            'posts_per_page' => -1,
            's' => $novel_title,
            'post_status' => 'publish',
            'post__not_in' => [$novel_id]
        ]);
        
        // Combine and deduplicate
        $all_potential_chapters = array_merge($slug_chapters, $title_chapters);
        $unique_chapters = [];
        foreach ($all_potential_chapters as $chapter) {
            if (!isset($unique_chapters[$chapter->ID])) {
                $unique_chapters[$chapter->ID] = $chapter;
            }
        }
        
        // Filter out chapters that already have correct _novel_id
        $chapters_to_fix = [];
        foreach ($unique_chapters as $chapter) {
            $existing_novel_id = get_post_meta($chapter->ID, '_novel_id', true);
            if (empty($existing_novel_id) || $existing_novel_id != $novel_id) {
                $chapters_to_fix[] = $chapter;
            }
        }
        
        $existing_count = count($existing_chapters);
        $to_fix_count = count($chapters_to_fix);
        $total_chapters = $existing_count + $to_fix_count;
        
        echo '<tr>';
        echo '<td>' . $novel_id . '</td>';
        echo '<td>' . esc_html($novel_title) . '</td>';
        echo '<td>' . $novel->post_type . '</td>';
        echo '<td>' . $existing_count . ' existing + ' . $to_fix_count . ' to fix = ' . $total_chapters . ' total</td>';
        
        if ($to_fix_count > 0) {
            if (!$dry_run) {
                // Actually fix the chapters
                $fixed_count = 0;
                foreach ($chapters_to_fix as $chapter) {
                    $result = update_post_meta($chapter->ID, '_novel_id', $novel_id);
                    if ($result) {
                        $fixed_count++;
                    }
                }
                echo '<td class="success">Fixed ' . $fixed_count . ' chapters</td>';
                $total_fixed += $fixed_count;
            } else {
                echo '<td class="warning">Would fix ' . $to_fix_count . ' chapters</td>';
            }
        } else {
            echo '<td class="info">No action needed</td>';
        }
        echo '</tr>';
        
        // Show details of chapters to be fixed
        if ($to_fix_count > 0 && $to_fix_count <= 10) {
            echo '<tr><td colspan="5"><small>';
            echo 'Chapters to fix: ';
            foreach ($chapters_to_fix as $chapter) {
                echo $chapter->ID . ' (' . esc_html($chapter->post_title) . '), ';
            }
            echo '</small></td></tr>';
        }
    }
    
    echo '</table>';
    
    if (!$dry_run && $total_fixed > 0) {
        echo '<div class="success">';
        echo '<h4>✅ Berhasil memperbaiki ' . $total_fixed . ' chapters!</h4>';
        echo '<p>Meta field _novel_id telah ditambahkan/diupdate.</p>';
        echo '</div>';
    }
    
} else {
    echo '<p class="error">Tidak ada novels ditemukan. Pastikan Anda memiliki posts dengan post_type "novel" atau posts dengan meta field novel.</p>';
}

echo '</div>';

// Additional tools
echo '<div class="section">';
echo '<h3>Tools Tambahan</h3>';
echo '<p><a href="debug-chapters.php?novel_id=' . (isset($novels[0]) ? $novels[0]->ID : '1') . '" class="btn btn-primary">Debug Chapters</a></p>';
echo '<p><a href="?" class="btn btn-primary">Reset / Kembali ke Awal</a></p>';
echo '</div>';

endif; // End confirm check
?>

<div class="section">
    <h3>Cara Kerja Script Ini</h3>
    <ol>
        <li><strong>Mencari Novels:</strong> Script mencari posts dengan post_type "novel" atau posts yang memiliki meta field novel-related</li>
        <li><strong>Mencari Chapters:</strong> Untuk setiap novel, script mencari posts yang kemungkinan adalah chapters berdasarkan:
            <ul>
                <li>Novel slug dalam title/content</li>
                <li>Novel title dalam title/content</li>
            </ul>
        </li>
        <li><strong>Memperbaiki Meta:</strong> Script menambahkan/mengupdate meta field "_novel_id" pada chapters yang ditemukan</li>
        <li><strong>Validasi:</strong> Script tidak akan mengubah chapters yang sudah memiliki _novel_id yang benar</li>
    </ol>
</div>

</body>
</html>
