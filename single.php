<?php get_header(); ?>

<!-- Breadcrumb Navigation -->
<?php ln_reader_breadcrumbs(); ?>

<?php if (have_posts()) : while (have_posts()) : the_post();
    $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
    $chapter_number = get_post_meta(get_the_ID(), '_chapter_number', true);
    $chapter_title = get_post_meta(get_the_ID(), '_chapter_title', true);
    $novel = get_post($novel_id);
?>

<!-- Structured Data for Chapter -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Chapter",
    "name": "<?php echo esc_js(get_the_title()); ?>",
    "url": "<?php echo esc_url(get_permalink()); ?>",
    "description": "<?php echo esc_js(sprintf(__('Chapter %s of %s', 'ln-reader'), $chapter_number, $novel ? $novel->post_title : 'Unknown Novel')); ?>",
    "position": "<?php echo esc_js($chapter_number); ?>",
    "datePublished": "<?php echo esc_js(get_the_date('c')); ?>",
    "dateModified": "<?php echo esc_js(get_the_modified_date('c')); ?>",
    "author": {
        "@type": "Person",
        "name": "<?php echo esc_js(get_the_author()); ?>"
    },
    <?php if ($novel) : ?>
    "isPartOf": {
        "@type": "Book",
        "name": "<?php echo esc_js($novel->post_title); ?>",
        "url": "<?php echo esc_url(get_permalink($novel)); ?>"
    },
    <?php endif; ?>
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
        "url": "<?php echo esc_url(home_url()); ?>"
    }
}
</script>

<div class="theme-content-wrapper">
<div class="container chapter-container">

            <!-- Reading Container -->

                <!-- Chapter Top Ad Placement -->
                <?php if (is_active_sidebar('chapter-top-ads')) : ?>
                    <div class="chapter-top-ads">
                        <?php dynamic_sidebar('chapter-top-ads'); ?>
                    </div>
                <?php endif; ?>
            <!-- Chapter Header -->
            <header class="chapter-header">
                <div class="chapter-title-section">
                    <?php if ($novel) : ?>
                        <div class="novel-breadcrumb">
                            <a href="<?php echo esc_url(get_permalink($novel)); ?>" class="novel-link">
                                <?php echo esc_html($novel->post_title); ?>
                            </a>
                        </div>
                    <?php endif; ?>

                    <h1 class="chapter-title">
                        <?php esc_html_e('Chapter', 'ln-reader'); ?> <?php echo esc_html($chapter_number); ?>
                        <?php if ($chapter_title) : ?>
                            <span class="chapter-subtitle"><?php echo esc_html($chapter_title); ?></span>
                        <?php endif; ?>
                    </h1>

                    <div class="chapter-meta">
                        <time class="chapter-date" datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                            <?php echo get_the_date(); ?>
                        </time>
                        <span class="chapter-author">
                            <?php esc_html_e('by', 'ln-reader'); ?> <?php the_author(); ?>
                        </span>
                    </div>
                </div>
            </header>

            <!-- Chapter Navigation Top -->
            <nav class="chapter-navigation chapter-nav-top" role="navigation" aria-label="<?php esc_attr_e('Chapter Navigation', 'ln-reader'); ?>">
                <div class="nav-controls">
                    <?php if ($novel) : ?>
                        <a href="<?php echo esc_url(get_permalink($novel)); ?>" class="chapter-nav-btn btn-outline" aria-label="<?php esc_attr_e('View chapter list', 'ln-reader'); ?>">
                            <i class="fas fa-list" aria-hidden="true"></i>
                            <span><?php esc_html_e('Chapters', 'ln-reader'); ?></span>
                        </a>
                    <?php endif; ?>

                    <?php
                    $prev_chapter = function_exists('get_prev_chapter') ? get_prev_chapter($post->ID, $novel_id) : null;
                    if ($prev_chapter) :
                        $prev_url = get_permalink($prev_chapter->ID);
                        $prev_number = get_post_meta($prev_chapter->ID, '_chapter_number', true);
                    ?>
                        <a href="<?php echo esc_url($prev_url); ?>" class="chapter-nav-btn btn-primary" aria-label="<?php echo esc_attr(sprintf(__('Previous chapter %s', 'ln-reader'), $prev_number)); ?>">
                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                            <span><?php esc_html_e('Previous', 'ln-reader'); ?></span>
                        </a>
                    <?php else : ?>
                        <button class="chapter-nav-btn btn-disabled" disabled aria-label="<?php esc_attr_e('No previous chapter', 'ln-reader'); ?>">
                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                            <span><?php esc_html_e('Previous', 'ln-reader'); ?></span>
                        </button>
                    <?php endif; ?>

                    <?php
                    $next_chapter = function_exists('get_next_chapter') ? get_next_chapter($post->ID, $novel_id) : null;
                    if ($next_chapter) :
                        $next_url = get_permalink($next_chapter->ID);
                        $next_number = get_post_meta($next_chapter->ID, '_chapter_number', true);
                    ?>
                        <a href="<?php echo esc_url($next_url); ?>" class="chapter-nav-btn btn-primary" aria-label="<?php echo esc_attr(sprintf(__('Next chapter %s', 'ln-reader'), $next_number)); ?>">
                            <span><?php esc_html_e('Next', 'ln-reader'); ?></span>
                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                        </a>
                    <?php else : ?>
                        <button class="chapter-nav-btn btn-disabled" disabled aria-label="<?php esc_attr_e('No next chapter', 'ln-reader'); ?>">
                            <span><?php esc_html_e('Next', 'ln-reader'); ?></span>
                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                        </button>
                    <?php endif; ?>
                </div>

                <div class="reading-settings">
                    <button class="reading-settings-btn" id="theme-toggle" aria-label="<?php esc_attr_e('Toggle reading theme', 'ln-reader'); ?>">
                        <i class="fas fa-palette" aria-hidden="true"></i>
                    </button>
                    <button class="reading-settings-btn" id="font-size-toggle" aria-label="<?php esc_attr_e('Adjust font size', 'ln-reader'); ?>">
                        <i class="fas fa-text-height" aria-hidden="true"></i>
                    </button>
                </div>
            </nav>

            <!-- Chapter Content -->
            <article class="chapter-content" role="main" itemprop="articleBody">
                    <?php
                    $content = get_the_content();
                    if ($content) :
                    ?>
                        <div class="chapter-text" id="chapter-text">
                            <?php echo wp_kses_post(wpautop($content)); ?>
                        </div>
                    <?php else : ?>
                        <div class="no-content-message">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <p><?php esc_html_e('This chapter content is not available.', 'ln-reader'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </article>

            <!-- Chapter Middle Ads -->
            <?php if (is_active_sidebar('chapter-middle-ads')) : ?>
                <div class="chapter-middle-ads" role="complementary" aria-label="<?php esc_attr_e('Chapter advertisements', 'ln-reader'); ?>">
                    <?php dynamic_sidebar('chapter-middle-ads'); ?>
                </div>
            <?php endif; ?>

            <!-- Chapter Navigation Bottom -->
            <nav class="chapter-navigation chapter-nav-bottom" role="navigation" aria-label="<?php esc_attr_e('Chapter Navigation', 'ln-reader'); ?>">
                <div class="nav-controls">
                    <?php if ($prev_chapter) : ?>
                        <a href="<?php echo esc_url($prev_url); ?>" class="chapter-nav-btn btn-primary" aria-label="<?php echo esc_attr(sprintf(__('Previous chapter %s', 'ln-reader'), $prev_number)); ?>">
                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                            <span><?php esc_html_e('Previous', 'ln-reader'); ?></span>
                        </a>
                    <?php else : ?>
                        <button class="chapter-nav-btn btn-disabled" disabled aria-label="<?php esc_attr_e('No previous chapter', 'ln-reader'); ?>">
                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                            <span><?php esc_html_e('Previous', 'ln-reader'); ?></span>
                        </button>
                    <?php endif; ?>

                    <div class="chapter-nav-center">
                        <?php if ($novel) : ?>
                            <a href="<?php echo esc_url(get_permalink($novel)); ?>" class="chapter-nav-btn btn-outline" aria-label="<?php esc_attr_e('View chapter list', 'ln-reader'); ?>">
                                <i class="fas fa-list" aria-hidden="true"></i>
                                <span><?php esc_html_e('Chapters', 'ln-reader'); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>

                    <?php if ($next_chapter) : ?>
                        <a href="<?php echo esc_url($next_url); ?>" class="chapter-nav-btn btn-primary" aria-label="<?php echo esc_attr(sprintf(__('Next chapter %s', 'ln-reader'), $next_number)); ?>">
                            <span><?php esc_html_e('Next', 'ln-reader'); ?></span>
                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                        </a>
                    <?php else : ?>
                        <button class="chapter-nav-btn btn-disabled" disabled aria-label="<?php esc_attr_e('No next chapter', 'ln-reader'); ?>">
                            <span><?php esc_html_e('Next', 'ln-reader'); ?></span>
                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                        </button>
                    <?php endif; ?>
                </div>
            </nav>

            <!-- Social Share Section -->
            <section class="social-share-section" role="complementary">
                <h3 class="share-title"><?php esc_html_e('Share this chapter', 'ln-reader'); ?></h3>
                <div class="share-buttons">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>"
                       target="_blank" rel="noopener" class="share-btn share-facebook" aria-label="<?php esc_attr_e('Share on Facebook', 'ln-reader'); ?>">
                        <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        <span><?php esc_html_e('Facebook', 'ln-reader'); ?></span>
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>"
                       target="_blank" rel="noopener" class="share-btn share-twitter" aria-label="<?php esc_attr_e('Share on Twitter', 'ln-reader'); ?>">
                        <i class="fab fa-twitter" aria-hidden="true"></i>
                        <span><?php esc_html_e('Twitter', 'ln-reader'); ?></span>
                    </a>
                    <a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_the_title() . ' ' . get_permalink()); ?>"
                       target="_blank" rel="noopener" class="share-btn share-whatsapp" aria-label="<?php esc_attr_e('Share on WhatsApp', 'ln-reader'); ?>">
                        <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        <span><?php esc_html_e('WhatsApp', 'ln-reader'); ?></span>
                    </a>
                    <button class="share-btn share-copy" id="copy-url" aria-label="<?php esc_attr_e('Copy URL', 'ln-reader'); ?>">
                        <i class="fas fa-link" aria-hidden="true"></i>
                        <span><?php esc_html_e('Copy Link', 'ln-reader'); ?></span>
                    </button>
                </div>
            </section>
            
            <!-- Chapter Bottom Ad Placement -->
            <?php if (is_active_sidebar('chapter-bottom-ads')) : ?>
                <div class="chapter-bottom-ads">
                    <?php dynamic_sidebar('chapter-bottom-ads'); ?>
                </div>
            <?php endif; ?>

            <!-- Comments Section -->
            <div class="comments-section p-4 rounded shadow-sm mb-4">
                <?php
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                ?>
            </div>
        </div>
    <?php endwhile; endif; ?>
    </div>
</div>

<style>
/* Theme Transitions */
.chapter-content, 
.chapter-nav, 
.breadcrumb, 
.chapter-text,
.modal-content,
.modal-header,
.modal-body,
pre,
code,
blockquote,
.btn-close,
.form-select,
.form-label,
.btn,
.container,
.reading-container * {
    transition: all 0.3s ease;
}

/* Base Styles */
.reading-container {
    max-width: 900px;
    margin: 0 auto;
}

/* Chapter Content */
.chapter-content {
    background-color: var(--bg-color, #fff);
    color: var(--text-color, #333);
    transition: all 0.3s ease;
}

.chapter-text {
    font-family: 'Fira Sans', sans-serif;
    font-size: var(--font-size, 18px);
    line-height: var(--line-height, 1.8);
    color: var(--text-color, #333);
}

.chapter-title {
    color: var(--title-color, #2c3e50);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.chapter-subtitle {
    color: var(--subtitle-color, #666);
    font-size: 18px;
}

.chapter-text {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: 4px;
}

.chapter-text p {
    margin-bottom: 1.5rem;
}

/* Navigation Buttons */
.chapter-nav .btn {
    font-weight: 500;
}

.btn-facebook { background-color: #1877f2; color: white; }
.btn-twitter { background-color: #1da1f2; color: white; }
.btn-whatsapp { background-color: #25d366; color: white; }

.btn-facebook:hover,
.btn-twitter:hover,
.btn-whatsapp:hover {
    color: white;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chapter-content {
        padding: 1rem;
    }

    .chapter-nav .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .chapter-title {
        font-size: 20px;
    }

    .chapter-subtitle {
        font-size: 16px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body.dark-mode .chapter-content,
    body.dark-mode .chapter-nav,
    body.dark-mode .breadcrumb {
        background-color: #1a1a1a !important;
        color: #e0e0e0;
    }

    body.dark-mode .chapter-title {
        color: #fff;
    }

    body.dark-mode .chapter-subtitle {
        color: #aaa;
    }

    body.dark-mode .btn-light {
        background-color: #333;
        border-color: #444;
        color: #fff;
    }
}

/* Theme Styles */
body {
    transition: background-color 0.3s ease;
}

.chapter-content,
.chapter-nav,
.breadcrumb {
    transition: all 0.3s ease;
}

.breadcrumb-item a,
.chapter-title,
.chapter-subtitle {
    transition: color 0.3s ease;
}

/* Sepia Theme */
.sepia-mode {
    background-color: #e8dcbf;
}

/* Dark Theme */
.dark-mode {
    background-color: #121212;
}

/* Breadcrumb Styles */
.breadcrumb {
    background-color: var(--bg-color, #fff);
    transition: all 0.3s ease;
}

.breadcrumb-item a {
    color: var(--link-color, #0d6efd);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item.active {
    color: var(--text-muted, #6c757d);
}

/* Theme Colors */
[data-theme="dark"] {
    --bg-color: #121212;
    --content-bg: #1a1a1a;
    --text-color: #e0e0e0;
    --link-color: #4a9eff;
    --text-muted: #aaa;
    --title-color: #fff;
    --subtitle-color: #aaa;
}

[data-theme="sepia"] {
    --bg-color: #e8dcbf;
    --content-bg: #f4ecd8;
    --text-color: #5c4b37;
    --link-color: #8b6b4f;
    --text-muted: #8b6b4f;
    --title-color: #5c4b37;
    --subtitle-color: #8b6b4f;
}

[data-theme="light"] {
    --bg-color: #f8f9fa;
    --content-bg: #fff;
    --text-color: #333;
    --link-color: #0d6efd;
    --text-muted: #6c757d;
    --title-color: #2c3e50;
    --subtitle-color: #666;
}

/* Navigation Styles */
.chapter-nav {
    background-color: var(--bg-color, #fff);
    transition: all 0.3s ease;
}

.chapter-nav .btn-outline-primary {
    color: var(--link-color, #0d6efd);
    border-color: var(--link-color, #0d6efd);
}

.chapter-nav .btn-outline-primary:hover {
    background-color: var(--link-color, #0d6efd);
    color: #ffffff;
}

.chapter-nav .btn-primary {
    background-color: var(--link-color, #0d6efd);
    border-color: var(--link-color, #0d6efd);
    color: #ffffff;
}

.chapter-nav .btn-primary:hover {
    opacity: 0.9;
    color: #ffffff;
}

/* Ensure button text is always visible */
.chapter-nav-btn {
    color: #ffffff;
}

.chapter-nav-btn.btn-outline {
    color: var(--link-color, #0d6efd);
}

.chapter-nav-btn.btn-outline:hover {
    color: #ffffff;
}

.chapter-nav-btn span,
.chapter-nav-btn i {
    color: inherit;
}

.chapter-nav .btn-light {
    background-color: var(--btn-light-bg, #f8f9fa);
    border-color: var(--btn-light-border, #ddd);
    color: var(--text-color, #333);
}

/* Update Theme Colors */
[data-theme="dark"] {
    --btn-light-bg: #333;
    --btn-light-border: #444;
}

[data-theme="sepia"] {
    --btn-light-bg: #e8dcbf;
    --btn-light-border: #d4c8b0;
}

[data-theme="light"] {
    --btn-light-bg: #f8f9fa;
    --btn-light-border: #ddd;
}

/* Theme Content Wrapper */
.theme-content-wrapper {
    background-color: var(--bg-color, #fff);
    min-height: calc(100vh - 140px);
    transition: background-color 0.3s ease;
    padding: 2rem 0;
}

.theme-content-wrapper .container {
    background-color: var(--bg-color, #fff);
    transition: background-color 0.3s ease;
}

/* Tambahkan CSS Variables untuk font */
:root {
    --font-size: 18px;
    --line-height: 1.8;
}

.meta-info {
    color: var(--text-color, #333);
    border-bottom: 1px solid var(--border-color, #dee2e6);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

/* Update Theme Colors */
[data-theme="dark"] {
    --border-color: #333;
}

[data-theme="sepia"] {
    --border-color: #d4c8b0;
}

[data-theme="light"] {
    --border-color: #dee2e6;
}

/* Chapter Header Styles */
.novel-title {
    color: var(--title-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.chapter-info {
    padding: 1rem 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    margin: 1rem 0;
}

.chapter-title {
    color: var(--title-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.chapter-subtitle {
    color: var(--subtitle-color);
    font-size: 1rem;
    font-style: italic;
}

.meta-details {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.meta-details i {
    margin-right: 0.3rem;
}
</style>

<!-- Reading Options Modal -->
<div class="modal fade" id="optionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reading Options</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Font Size</label>
                    <select class="form-select" id="fontSize">
                        <option value="16">Small</option>
                        <option value="18" selected>Medium</option>
                        <option value="20">Large</option>
                        <option value="22">Extra Large</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Line Height</label>
                    <select class="form-select" id="lineHeight">
                        <option value="1.6">Compact</option>
                        <option value="1.8" selected>Normal</option>
                        <option value="2">Relaxed</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Theme</label>
                    <select class="form-select" id="theme">
                        <option value="light" selected>Light</option>
                        <option value="sepia">Sepia</option>
                        <option value="dark">Dark</option>
                    </select>
                </div>
                <button class="btn btn-secondary" id="resetOptions">Reset to Default</button>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Load saved options
    const savedOptions = JSON.parse(localStorage.getItem('readingOptions') || '{}');
    
    if (savedOptions.fontSize) $('#fontSize').val(savedOptions.fontSize);
    if (savedOptions.lineHeight) $('#lineHeight').val(savedOptions.lineHeight);
    if (savedOptions.theme) $('#theme').val(savedOptions.theme);
    
    applyReadingOptions(savedOptions);

    // Handle option changes
    $('.form-select').on('change', function() {
        const options = {
            fontSize: $('#fontSize').val(),
            lineHeight: $('#lineHeight').val(),
            theme: $('#theme').val()
        };
        
        console.log('Applying options:', options); // Untuk debugging
        localStorage.setItem('readingOptions', JSON.stringify(options));
        applyReadingOptions(options);
    });

    // Reset options
    $('#resetOptions').click(function() {
        const defaultOptions = {
            fontSize: '18',
            lineHeight: '1.8',
            theme: 'light'
        };
        
        localStorage.setItem('readingOptions', JSON.stringify(defaultOptions));
        
        $('#fontSize').val(defaultOptions.fontSize);
        $('#lineHeight').val(defaultOptions.lineHeight);
        $('#theme').val(defaultOptions.theme);
        
        applyReadingOptions(defaultOptions);
    });

    function applyReadingOptions(options) {
        // Apply font size and line height
        if (options.fontSize) {
            document.documentElement.style.setProperty('--font-size', options.fontSize + 'px');
        }
        if (options.lineHeight) {
            document.documentElement.style.setProperty('--line-height', options.lineHeight);
        }

        // Apply theme using data-theme attribute system
        const themeWrapper = document.querySelector('.theme-content-wrapper');
        if (themeWrapper) {
            themeWrapper.setAttribute('data-theme', options.theme);
        }

        // Set theme on body and html for comprehensive coverage
        document.body.setAttribute('data-theme', options.theme);
        document.documentElement.setAttribute('data-theme', options.theme);

        // Remove any existing inline styles that might conflict with CSS
        removeConflictingInlineStyles();

        // Trigger theme change event for other components
        $(document).trigger('themeChanged', { theme: options.theme });
    }

    // Remove inline styles that conflict with the CSS theme system
    function removeConflictingInlineStyles() {
        const selectors = [
            '.theme-content-wrapper',
            '.theme-content-wrapper .container',
            '.chapter-content',
            '.chapter-nav',
            '.breadcrumb',
            '.chapter-text',
            '.modal-content',
            '.modal-header',
            '.modal-body',
            '.form-label',
            '.btn-secondary',
            '.btn-close',
            '.form-select',
            '.form-control',
            '.btn-outline-primary',
            'pre',
            'code',
            'blockquote',
            '.chapter-title',
            '.chapter-subtitle',
            '.chapter-meta',
            '.breadcrumb-item a',
            '.breadcrumb-item.active',
            '.btn-light',
            '.title',
            '.author',
            '.publication-status',
            '.first-published',
            '.last-updated',
            '.genre',
            '.tags',
            '.url',
            '.description'
        ];

        selectors.forEach(selector => {
            $(selector).css({
                'background-color': '',
                'color': '',
                'border-color': '',
                'filter': '',
                'box-shadow': ''
            });
        });
    }

    // Keyboard Navigation
    $(document).keydown(function(e) {
        if (e.keyCode === 37) { // Left Arrow
            const prevLink = $('.chapter-nav a:contains("Previous")').attr('href');
            if (prevLink) window.location.href = prevLink;
        }
        else if (e.keyCode === 39) { // Right Arrow
            const nextLink = $('.chapter-nav a:contains("Next")').attr('href');
            if (nextLink) window.location.href = nextLink;
        }
    });
});
</script>

<?php get_footer(); ?>
